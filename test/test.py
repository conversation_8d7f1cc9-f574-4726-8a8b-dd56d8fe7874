from app.models.user import User
from app.service.user import UserService
from app.utils.log import logger

if __name__ == '__main__':

    # config = yaml.yaml_to_object('config_dev.yml', config.ConfigModel)
    # print(config)
    service = UserService()
    user = User()
    user.login_name = 'wonstar3'
    user.username = 'wonstar11111'
    user.password = 'password'
    r = service.create(user)
    logger.info(r)
    pass