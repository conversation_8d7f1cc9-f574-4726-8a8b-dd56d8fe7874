import datetime

import time
import traceback
import uuid

from app.config.config import Config
from app.models.user import User
from app.utils import timeUtil


from redis.cluster import RedisCluster

from app.utils.log import logger
from app.utils.obj2dict import obj2dict
from app.utils.obj2json import obj2json, dict2json
from app.utils.redis import RedisUtils


def connect_redis_cluster_with_auth():

    try:
        r = RedisCluster(
            host='*************',
            port=6381,
            # startup_nodes=startup_nodes,
            password="zjxX8z*I!0E6D9P2HmJgmg5CY39EoGWe",  # 集群统一密码（Redis 6.2 支持）
            decode_responses=True,
            skip_full_coverage_check=True
        )
        print(r.info())
        # 测试认证
        ex_at = int(timeUtil.str_to_timestamp('2025-08-04 23:59:59'))
        print(ex_at)
        # ex_at = int(time.time()) + 3600  # 1小时后过期
        #r.set(name="test_key", value="test_value")
        #r.expireat(name="test_key",when=ex_at)
        # r.set("test_key", "password_ok11",ex=100)
        v = r.get("test_key")
        print("认证成功，值为：", v,type(v))
        print(get_key_ttl(r,'test_key'))
        return r
    except Exception as e:
        print("认证失败：", str(e))
        traceback.print_exc()
        return None
def get_key_ttl(r,key):
    """获取指定key的剩余过期时间（秒）"""
    try:
        ttl = r.ttl(key)
        if ttl == -1:
            return f"Key '{key}' 存在但没有设置过期时间"
        elif ttl == -2:
            return f"Key '{key}' 不存在"
        elif ttl == 0:
            return f"Key '{key}' 已过期"
        else:
            return f"Key '{key}' 剩余过期时间: {ttl} 秒"
    except Exception as e:
        return f"查询失败: {str(e)}"
#cluster = connect_redis_cluster_with_auth()
#
if __name__ == '__main__':
    # logger.info(Config.redis)
    # print(type(Config.redis))
    # redis = RedisUtils(Config.redis)
    # redis.set('username','wonstar')
    # value = redis.get('username')
    # print(value,len(value))
    # print(redis._conn.cluster("nodes"))
    token = str(20) + str(uuid.uuid1())
    user = User()
    user.id=20
    user.login_name='admin'
    data = {
        'token': token,
        'id': 20,
        'user': obj2json(user),
    }
    print(RedisUtils().set('token:'+token,dict2json(data),36000))
    pass