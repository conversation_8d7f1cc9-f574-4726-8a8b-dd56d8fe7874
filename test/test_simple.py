import sys
import os

# 添加项目根目录到Python路径
sys.path.append('.')

def test_imports():
    """测试基本导入"""
    try:
        print("测试导入 AIService...")
        from app.service.ai import AIService
        print("✅ AIService 导入成功")
        
        print("测试导入 ModelType...")
        from app.schema.ai import ModelType
        print("✅ ModelType 导入成功")
        
        print("测试创建 AIService 实例...")
        auth_data = {'user_id': 1}
        ai_service = AIService(auth_data)
        print("✅ AIService 实例创建成功")
        
        print("测试创建会话...")
        session_id = ai_service.create_session("测试对话")
        print(f"✅ 会话创建成功: {session_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=== 简单测试开始 ===")
    success = test_imports()
    if success:
        print("=== 所有测试通过 ===")
    else:
        print("=== 测试失败 ===")
        sys.exit(1)
