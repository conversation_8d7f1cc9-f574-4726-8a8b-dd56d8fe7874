import uvicorn

from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import logging

from fastapi import Fast<PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.security import OAuth2Password<PERSON>earer, OAuth2PasswordRequestForm
from fastapi.openapi.docs import get_swagger_ui_html, get_swagger_ui_oauth2_redirect_html
from fastapi.openapi.utils import get_openapi
from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

# 开启日志调试（方便排查问题）
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ------------------------------
# 核心配置与工具（保持不变）
# ------------------------------
SECRET_KEY = "your-secret-key-keep-it-safe-and-long-enough"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

fake_users_db: Dict[str, Dict[str, Any]] = {
    "johndoe": {
        "username": "johndoe",
        "full_name": "John Doe",
        "email": "<EMAIL>",
        "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # 密码 "secret"
        "disabled": False,
    }
}

class Token(BaseModel):
    access_token: str
    token_type: str

class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None

class UserInDB(User):
    hashed_password: str

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_user(db: Dict[str, Dict], username: str) -> Optional[UserInDB]:
    if username in db:
        return UserInDB(** db[username])
    return None

def authenticate_user(fake_db: Dict[str, Dict], username: str, password: str) -> Optional[UserInDB]:
    user = get_user(fake_db, username)
    if not user or not verify_password(password, user.hashed_password):
        return None
    return user

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + (expires_delta or timedelta(minutes=15))
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

# ------------------------------
# 认证依赖（关键：添加日志便于调试）
# ------------------------------
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

async def get_current_user(token: str = Depends(oauth2_scheme)) -> UserInDB:
    logger.info(f"正在验证 Token: {token[:10]}...")  # 打印部分 Token 便于调试
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None or not (user := get_user(fake_users_db, username)):
            raise credentials_exception
        return user
    except JWTError as e:
        logger.error(f"Token 解析失败: {str(e)}")
        raise credentials_exception

async def get_current_active_user(current_user: UserInDB = Depends(get_current_user)) -> UserInDB:
    # if current_user.disabled:
    #     raise HTTPException(status_code=400, detail="禁用的用户")
    return current_user

# ------------------------------
# 初始化 app（关键：先禁用默认 docs，再手动添加）
# ------------------------------
app = FastAPI(
    dependencies=[Depends(get_current_active_user)],  # 全局认证
    docs_url=None,  # 必须禁用默认 docs 路由
    redoc_url=None,
    openapi_url=None,  # 必须禁用默认 openapi 路由
    title="带文档例外的 FastAPI 应用"
)

# ------------------------------
# 优先定义 docs 相关路由（关键：确保路由优先被注册）
# ------------------------------
@app.get("/docs", include_in_schema=False, dependencies=[])
async def custom_swagger_ui_html(request: Request):
    logger.info("访问 /docs 路由（已例外）")  # 调试日志
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
    )

@app.get(app.swagger_ui_oauth2_redirect_url, include_in_schema=False, dependencies=[])
async def swagger_ui_redirect():
    logger.info("访问 OAuth2 重定向路由（已例外）")
    return get_swagger_ui_oauth2_redirect_html()

@app.get("/openapi.json", include_in_schema=False, dependencies=[])
async def get_openapi_json():
    logger.info("访问 /openapi.json 路由（已例外）")
    return get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

# ------------------------------
# 其他例外路由（如登录）
# ------------------------------
@app.post("/token", response_model=Token, dependencies=[])
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    logger.info(f"登录请求：用户名 {form_data.username}")
    user = authenticate_user(fake_users_db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(
        data={"sub": user.username},
        expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    return {"access_token": access_token, "token_type": "bearer"}

# ------------------------------
# 受保护的业务路由
# ------------------------------
@app.get("/users/me/", response_model=User)
async def read_users_me(request: Request):
    return {
        "username": request.state.user.username,
        "email": request.state.user.email
    }

# ------------------------------
# 中间件（避免干扰 docs 路由）
# ------------------------------
@app.middleware("http")
async def add_user_to_request(request: Request, call_next):
    # 关键：对 docs 相关路由跳过中间件的认证逻辑
    if request.url.path in ["/docs", "/openapi.json", app.swagger_ui_oauth2_redirect_url]:
        return await call_next(request)

    # 其他路由正常处理
    try:
        current_user = await get_current_active_user()
        request.state.user = current_user
    except HTTPException as e:
        logger.warning(f"认证失败：{e.detail}")
    response = await call_next(request)
    return response


if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8001)