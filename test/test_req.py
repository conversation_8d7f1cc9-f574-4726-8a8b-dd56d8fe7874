from bs4 import BeautifulSoup

# 假设HTML内容已保存在变量html_content中
# 如果是从文件读取，可以使用：
with open('aa.html',encoding='utf-8') as f: html_content = f.read()
soup = BeautifulSoup(html_content, 'html.parser')

# 查找包含沪深300收盘点的段落
target_paragraph = None
for p in soup.find_all('p'):
    if "沪深300" in p.text and "报收" in p.text:
        target_paragraph = p.text
        break

# 提取报收数值
if target_paragraph:
    # 使用字符串分割提取数值
    start_index = target_paragraph.find("报收") + 2
    end_index = target_paragraph.find("点", start_index)
    closing_value = target_paragraph[start_index:end_index].strip()
    print(f"沪深300收盘点: {closing_value}")
else:
    print("未找到沪深300收盘点信息")