import asyncio
import uuid
import os
import sys

# 添加项目根目录到Python路径
sys.path.append('.')

# 设置通义千问API密钥
os.environ["DASHSCOPE_API_KEY"] = 'sk-fe091f71e5694ed3b1ee997d4b15319f'

def test_imports():
    """
    测试导入是否正常
    """
    try:
        from app.service.ai import AIService
        from app.schema.ai import ModelType
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """
    测试基本功能
    """
    try:
        from app.service.ai import AIService
        from app.schema.ai import ModelType

        # 模拟认证数据
        auth_data = {'user_id': 1}

        # 创建AI服务
        ai_service = AIService(auth_data)
        print("✅ AIService 创建成功")

        # 创建会话
        session_id = ai_service.create_session("测试对话")
        print(f"✅ 创建会话成功: {session_id}")

        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_chat():
    """
    测试AI聊天功能（需要API密钥）
    """
    try:
        from app.service.ai import AIService
        from app.schema.ai import ModelType

        # 模拟认证数据
        auth_data = {'user_id': 1}

        # 创建AI服务
        ai_service = AIService(auth_data)

        # 创建会话
        session_id = ai_service.create_session("测试对话")
        print(f"创建会话: {session_id}")

        # 测试聊天
        user_message = "你好"
        print(f"用户消息: {user_message}")

        print("AI回复:")
        async for chunk in ai_service.chat_stream(session_id, user_message, ModelType.TONGYI):
            print(chunk, end="")

        print("\n\n获取历史消息:")
        history = ai_service.get_session_history(session_id)
        for msg in history:
            print(f"[{msg.type}] {msg.content[:50]}...")

    except Exception as e:
        print(f"❌ AI聊天测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    print("=== 开始测试 ===")

    # 测试导入
    if not test_imports():
        exit(1)

    # 测试基本功能
    if not test_basic_functionality():
        exit(1)

    # 测试AI聊天（可选，需要API密钥和数据库）
    print("\n=== 测试AI聊天功能 ===")
    try:
        asyncio.run(test_ai_chat())
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

    print("\n=== 测试完成 ===")
