import oracledb

# 数据库连接信息
username = "eom"  # 数据库用户名
password = "xinxinsoft"  # 数据库密码
dsn = "//*************:1521/ORCLPDB1"  # 格式：//主机IP:端口/服务名（或 SID）
# 若用 SID 连接：dsn = "host:port:SID"

try:
    # 建立连接
    connection = oracledb.connect(
        user=username,
        password=password,
        dsn=dsn
    )
    print("成功连接到 Oracle 数据库！")

    # 创建游标
    cursor = connection.cursor()

    # 执行 SQL 查询
    cursor.execute("SELECT * from AFR_SYSTEMUSER")  # 查询当前时间
    result = cursor.fetchone()
    print(f"当前数据库时间：{result}")

except oracledb.Error as e:
    error, = e.args
    print(f"连接失败：{error.message}")
finally:
    # 关闭游标和连接
    if 'cursor' in locals():
        cursor.close()
    if 'connection' in locals():
        connection.close()
        print("连接已关闭")