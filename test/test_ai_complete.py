"""
完整的AI聊天功能测试
包括数据库表结构检查、基本功能测试和AI聊天测试
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append('.')

# 设置通义千问API密钥（如果需要测试AI功能）
os.environ["DASHSCOPE_API_KEY"] = 'sk-fe091f71e5694ed3b1ee997d4b15319f'

def test_database_setup():
    """测试数据库设置"""
    try:
        print("1. 测试数据库连接...")
        import app.config.db
        from app.utils.db import DbUtils
        
        db = DbUtils(app.config.db.mysql_config)
        print("✅ 数据库连接成功")
        
        # 检查表是否存在
        from sqlalchemy import text
        
        # 检查 message 表
        check_message_table = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'message'
        """
        result = db.sess.execute(text(check_message_table)).fetchone()
        if result.count > 0:
            print("✅ message 表存在")
        else:
            print("❌ message 表不存在，请运行 scripts/update_message_table.py")
            return False
        
        # 检查 session 表
        check_session_table = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'session'
        """
        result = db.sess.execute(text(check_session_table)).fetchone()
        if result.count > 0:
            print("✅ session 表存在")
        else:
            print("❌ session 表不存在，请运行 scripts/update_message_table.py")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_imports():
    """测试导入"""
    try:
        print("2. 测试模块导入...")
        
        from app.service.ai import AIService
        print("✅ AIService 导入成功")
        
        from app.schema.ai import ModelType, ChatRequestSchema
        print("✅ AI Schema 导入成功")
        
        from app.models.ai.message import Message, MsgType, Session
        print("✅ AI Models 导入成功")
        
        from app.core.dao.ai.message import MessageDao
        print("✅ MessageDao 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("3. 测试基本功能...")
        
        from app.service.ai import AIService
        from app.schema.ai import ModelType
        
        # 创建AI服务实例
        auth_data = {'user_id': 1}
        ai_service = AIService(auth_data)
        print("✅ AIService 实例创建成功")
        
        # 测试创建会话
        session_id = ai_service.create_session("测试对话")
        print(f"✅ 会话创建成功: {session_id}")
        
        # 测试保存用户消息
        user_msg = ai_service.save_user_message(session_id, "测试消息")
        print(f"✅ 用户消息保存成功: {user_msg.id}")
        
        # 测试保存AI消息
        ai_msg = ai_service.save_assistant_message(session_id, "测试AI回复", "tongyi")
        print(f"✅ AI消息保存成功: {ai_msg.id}")
        
        # 测试获取历史消息
        history = ai_service.get_session_history(session_id)
        print(f"✅ 历史消息获取成功，共 {len(history)} 条消息")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_chat():
    """测试AI聊天功能（需要API密钥）"""
    try:
        print("4. 测试AI聊天功能...")
        
        from app.service.ai import AIService
        from app.schema.ai import ModelType
        
        # 创建AI服务实例
        auth_data = {'user_id': 1}
        ai_service = AIService(auth_data)
        
        # 创建新会话
        session_id = ai_service.create_session("AI聊天测试")
        print(f"创建会话: {session_id}")
        
        # 测试简单对话
        user_message = "你好"
        print(f"用户消息: {user_message}")
        
        print("AI回复: ", end="")
        response_content = ""
        async for chunk in ai_service.chat_stream(session_id, user_message, ModelType.TONGYI):
            if 'data: ' in chunk:
                # 解析SSE数据
                import json
                try:
                    data_str = chunk.split('data: ')[1].strip()
                    data = json.loads(data_str)
                    if data.get('type') == 'chunk':
                        content = data.get('content', '')
                        response_content += content
                        print(content, end="", flush=True)
                    elif data.get('type') == 'done':
                        print(f"\n✅ 对话完成，消息ID: {data.get('message_id')}")
                        break
                    elif data.get('type') == 'error':
                        print(f"\n❌ 对话出错: {data.get('content')}")
                        break
                except:
                    pass
        
        # 检查历史消息
        print("\n历史消息:")
        history = ai_service.get_session_history(session_id)
        for msg in history:
            msg_type = "系统" if msg.type == 1 else "用户" if msg.type == 2 else "AI"
            print(f"[{msg_type}] {msg.content[:50]}{'...' if len(msg.content) > 50 else ''}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI聊天测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== AI聊天服务完整测试 ===\n")
    
    # 1. 测试数据库
    if not test_database_setup():
        print("\n❌ 数据库测试失败，请先运行 scripts/update_message_table.py 更新数据库表结构")
        return False
    
    # 2. 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败")
        return False
    
    # 3. 测试基本功能
    if not test_basic_functionality():
        print("\n❌ 基本功能测试失败")
        return False
    
    # 4. 测试AI聊天（可选）
    print("\n=== 开始AI聊天测试 ===")
    print("注意：此测试需要有效的通义千问API密钥")
    
    try:
        asyncio.run(test_ai_chat())
        print("\n✅ AI聊天测试完成")
    except Exception as e:
        print(f"\n❌ AI聊天测试失败: {e}")
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中出现未预期的错误: {e}")
        import traceback
        traceback.print_exc()
