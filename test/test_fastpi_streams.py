import asyncio

import uvicorn
from fastapi import FastAPI
from starlette.responses import StreamingResponse
import time

app = FastAPI()

async def generate_data():
    for i in range(10):
        # await asyncio.sleep(1)
        yield f"Chunk {i}\n"


@app.get("/stream")
async def stream_data():
    return StreamingResponse(generate_data(), media_type="text/plain")

if __name__ == '__main__':
    uvicorn.run(app, host="127.0.0.1", port=8001)