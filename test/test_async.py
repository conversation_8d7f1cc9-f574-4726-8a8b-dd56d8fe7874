import asyncio
import datetime
import time


async def fun(name,duration):
    await asyncio.sleep(duration)
    print(f"fun[{name}]已完成，耗时{duration}秒")



async def main():
    print(f'开始时间：{datetime.datetime.now()}')
    task1 = asyncio.create_task(fun("FUN1",1))
    task2 = asyncio.create_task(fun("FUN2", 2))
    task3 = asyncio.create_task(fun("FUN3", 2))
    task4 = asyncio.create_task(fun("FUN4", 4))
    task5 = asyncio.create_task(fun("FUN5", 5))
    # 等待所有任务完成
    await asyncio.gather(task1, task2, task3,task4,task5)
    print(f"执行完成｛｝{datetime.datetime.now()}")
    pass


if __name__ == '__main__':
    asyncio.run(main())
    pass
