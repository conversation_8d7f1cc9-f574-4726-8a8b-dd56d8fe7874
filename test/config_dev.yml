db:
  driver: mysql+pymysql
  host: *************
  port: 3306
  username: root
  password: bR2Y1z40x&fAQ6e%%&NBHB^lSP#EJhHo
  database: ai
  charset: utf8mb4
  table_name_prefix: t_
  echo: true
  pool_size: 100
  max_overflow: 100
  pool_recycle: 60
fastapi:
  title: AI Chat
  description: 基于大模型的AI聊天服务
  version: 2025.07.16
  openapi_url: /openapi.json
  openapi_prefix: ''
  docs_url: /docs
  redoc_url: /redoc
  static_url: /static/swagger-ui
  swagger_ui_oauth2_redirect_url: /docs/oauth2-redirect
  swagger_js_url: /static/swagger-ui/swagger-ui-bundle.js
  swagger_css_url: /static/swagger-ui/swagger-ui.css
  swagger_ui_init_oauth: null
  res_path: ../res
  request_log_to_mysql: true
  lifespan: null
mongo:
  host: 127.0.0.1
  port: 27017
  username: root
  password: 1q2w3e4R
  database: fastapi_demo
redis:
  host: *************
  port: 6381
  cluster:
    nodes:
      - *************:6381
      - *************:6382
      - *************:6383
      - *************:6381
      - *************:6382
      - *************:6383
  pool:
    # 连接池最大连接数（使用负值表示没有限制）
    max_connections: 200
    # 连接池中的最大空闲连接
    max_idle: 10
    # 连接池最大阻塞等待时间（使用负值表示没有限制）
    max_wait: -1
    # 连接池中的最小空闲连接
    min_idle: 0
  database: 0
  timeout: 5000
  password: zjxX8z*I!0E6D9P2HmJgmg5CY39EoGWe

  # username: root
