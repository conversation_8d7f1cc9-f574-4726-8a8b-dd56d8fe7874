"""
测试 FastapiConfig 类型注解是否正常工作
"""
import sys
sys.path.append('.')

def test_fastapi_config():
    """测试 FastapiConfig 配置类"""
    try:
        from app.config.fastapi import FastapiConfig
        
        print("=== 测试 FastapiConfig 类型注解 ===")
        
        # 测试类属性访问
        print(f"✅ title: {FastapiConfig.title} (类型: {type(FastapiConfig.title)})")
        print(f"✅ debug: {FastapiConfig.debug} (类型: {type(FastapiConfig.debug)})")
        print(f"✅ version: {FastapiConfig.version} (类型: {type(FastapiConfig.version)})")
        print(f"✅ docs_url: {FastapiConfig.docs_url} (类型: {type(FastapiConfig.docs_url)})")
        print(f"✅ request_log_to_mongo: {FastapiConfig.request_log_to_mongo} (类型: {type(FastapiConfig.request_log_to_mongo)})")
        
        # 测试 __dict__ 访问（用于 FastAPI 初始化）
        config_dict = FastapiConfig.__dict__
        print(f"\n✅ __dict__ 包含的配置项数量: {len([k for k, v in config_dict.items() if not k.startswith('_')])}")
        
        # 显示所有配置项
        print("\n📋 所有配置项:")
        for key, value in config_dict.items():
            if not key.startswith('_') and not callable(value):
                print(f"  {key}: {value} ({type(value).__name__})")
        
        # 测试类型注解
        print(f"\n✅ 类型注解检查:")
        annotations = FastapiConfig.__annotations__
        print(f"  注解数量: {len(annotations)}")
        for attr_name, attr_type in annotations.items():
            print(f"  {attr_name}: {attr_type}")
        
        # 模拟 FastAPI 初始化
        print(f"\n✅ 模拟 FastAPI 初始化测试:")
        fastapi_params = {k: v for k, v in config_dict.items() 
                         if not k.startswith('_') and not callable(v) and k != 'lifespan'}
        print(f"  可用于 FastAPI 的参数: {list(fastapi_params.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compatibility():
    """测试与现有代码的兼容性"""
    try:
        from app.config.fastapi import FastapiConfig
        
        print("\n=== 测试兼容性 ===")
        
        # 测试 main.py 中的用法: FastAPI(**FastapiConfig.__dict__)
        config_dict = {k: v for k, v in FastapiConfig.__dict__.items() 
                      if not k.startswith('_') and not callable(v)}
        print(f"✅ FastAPI 配置字典创建成功，包含 {len(config_dict)} 个参数")
        
        # 测试 base.py 中的用法: FastapiConfig.title
        title = FastapiConfig.title
        description = FastapiConfig.description
        version = FastapiConfig.version
        print(f"✅ 直接属性访问成功: {title}, {description}, {version}")
        
        # 测试 request_log.py 中的用法: FastapiConfig.request_log_to_mongo
        log_to_mongo = FastapiConfig.request_log_to_mongo
        print(f"✅ 布尔属性访问成功: request_log_to_mongo = {log_to_mongo}")
        
        return True
        
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=== FastapiConfig 类型注解测试 ===\n")
    
    success1 = test_fastapi_config()
    success2 = test_compatibility()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！FastapiConfig 类型注解添加成功")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)
