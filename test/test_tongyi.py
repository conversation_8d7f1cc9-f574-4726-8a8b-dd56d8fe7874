import asyncio
import os
from langchain_community.llms import Tongyi
from langchain_core.messages import HumanMessage, AIMessage
from starlette.responses import StreamingResponse

os.environ["DASHSCOPE_API_KEY"] = 'sk-fe091f71e5694ed3b1ee997d4b15319f'
from langchain.chat_models import ChatAnthropic

model = Tongyi()
chunks = []
async def test():

    async for chunk in model.astream([
        HumanMessage(content="Python 连接 Mysql数据库"),
        ]):

        chunks.append(chunk)
        print(chunk, end="", flush=True)

if __name__ == '__main__':
    asyncio.run(test())
    print(chunks)