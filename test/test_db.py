import datetime

import sqlalchemy.sql.schema
from sqlalchemy.orm import declarative_base, InstrumentedAttribute
from sqlalchemy.testing.schema import Column

import app.config.db
from app.config import config
from app.config.config import Config
from app.core.dao.ai.message import MessageDao
from app.core.dao.user import UserDao
from app.models.ai.message import Message, MsgType, ModelType
from app.models.basemodel import BaseModel
from app.models.user import User
from app.utils import yaml
from app.utils.db import DbUtils
from app.utils.log import logger
from app.utils.obj2json import obj2json


def get_all_class_attributes(cls):
    """递归获取类及其所有基类的属性（不包括方法）"""
    attributes = {}
    # 获取当前类的属性
    for attr, value in cls.__dict__.items():
        # 过滤方法和魔法属性
        # print(attr, value,type(value),type(value) == sqlalchemy.orm.attributes.InstrumentedAttribute)
        if type(value) is Column or type(value) is InstrumentedAttribute:
            attributes[attr] = value
        # if not attr.startswith("__") and not callable(value):
        #     attributes[attr] = value
    # 递归处理基类
    for base in cls.__bases__:
        base_attrs = get_all_class_attributes(base)
        attributes.update(base_attrs)  # 基类属性会被子类同名属性覆盖
    return attributes


if __name__ == '__main__':

    # config = yaml.yaml_to_object('config_dev.yml', config.ConfigModel)
    # print(config)
    dao = UserDao()
    user = User()
    user.login_name = 'wonstar3'
    user.username = 'wonstar11111'
    user.password = 'password'
    user.id = 3
    user = dao.read(20)
    logger.info(obj2json(user))
    pass