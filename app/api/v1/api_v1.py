from fastapi import APIRouter


from app.api.controller import ai, user

api_v1_router = APIRouter()
# api_v1_router.include_router(category.router, prefix="/category", tags=["category"])
# api_v1_router.include_router(org.router, prefix="/org", tags=["org"])
api_v1_router.include_router(user.router, prefix="/user", tags=["user"])
# api_v1_router.include_router(demo.router, prefix="/demo", tags=["demo"])

# 注册AI聊天路由
api_v1_router.include_router(ai.router, prefix="/ai", tags=["AI聊天"])

