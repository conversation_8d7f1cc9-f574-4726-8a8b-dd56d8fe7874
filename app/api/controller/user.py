from fastapi import APIRouter
from fastapi.params import Header

from app.utils.custom_route import CustomRoute

from app.schema.auth import AuthDataSchema, LoginInputSchema, UserSchema
from app.service.user import UserService

router = APIRouter(route_class=CustomRoute)


@router.post('/login', response_model=AuthDataSchema, response_model_exclude_unset=True)
async def login(*, login_input: LoginInputSchema):
    """
    用户登录
    :param login_input: 登录结构
    :return:
    """
    if login_input.type == 'token':
        return UserService().login_by_token(login_input.token)
    elif login_input.type == 'openid':
        return UserService().login_by_openid(login_input.key)
    elif login_input.type == 'code':
        return UserService().login_by_code(login_input.key)
    elif login_input.type == 'password':
        return UserService().login_by_password(login_input.login_name, login_input.password)
    else:
        return {
            'code': 500,
            'message': '登录错误',
        }
@router.post('/create')
async def create(user:UserSchema):
    return UserService().create(user)

@router.get(path='/{id}')
async def read(id:int):
    return UserService().read(id)