import datetime

from fastapi import APIRouter
from fastapi.openapi.docs import get_swagger_ui_html, get_swagger_ui_oauth2_redirect_html, get_redoc_html
from starlette.responses import Response

from app.config.config import Config

base_router = APIRouter()


@base_router.get('/')
async def get_root():
    """
    访问根路径
    """
    return {
        'title': Config.fastapi.title,
        'description':  Config.fastapi.description,
        'version':  Config.fastapi.version,
        'time': datetime.datetime.now()
    }


@base_router.get('/robots.txt')
async def get_robots():
    """
    获取爬虫权限
    """
    return Response(content='User-agent: * \nDisallow: /', media_type='text/plain')


@base_router.get('/sys_info')
async def get_sys_info():
    """
    获取系统基本信息
    """
    from app.service.sys_info import SysInfoService
    return SysInfoService().get_sys_info()

@base_router.get("/docs", include_in_schema=True)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=Config.fastapi.openapi_url,
        title=Config.fastapi.title + " - Swagger UI",
        oauth2_redirect_url=Config.fastapi.swagger_ui_oauth2_redirect_url,
        swagger_js_url="/static/swagger-ui/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui/swagger-ui.css",
    )


@base_router.get(Config.fastapi.swagger_ui_oauth2_redirect_url, include_in_schema=False)
async def swagger_ui_redirect():
    return get_swagger_ui_oauth2_redirect_html()


@base_router.get("/redoc", include_in_schema=True)
async def redoc_html():
    return get_redoc_html(
        openapi_url=Config.fastapi.openapi_url,
        title=Config.fastapi.title + " - ReDoc",
        redoc_js_url="/static/redoc/redoc.standalone.js",
    )