import asyncio
from typing import List
from datetime import datetime

from fastapi import APIRout<PERSON>, Depends, HTTPException
from starlette.responses import StreamingResponse

from app.utils.custom_route import CustomRoute
from app.utils.auth import get_auth_data
from app.service.ai import AIService
from app.schema.ai import (
    ChatRequestSchema,
    ChatResponseSchema,
    SessionCreateSchema,
    SessionResponseSchema,
    ChatHistoryResponseSchema,
    MessageHistorySchema
)
from app.schema.base import RespBaseSchema

router = APIRouter(route_class=CustomRoute)


@router.post("/chat/stream", tags=["AI聊天"])
async def chat_stream(request: ChatRequestSchema, auth_data: dict = Depends(get_auth_data)):
    """
    AI流式聊天接口
    """
    try:
        ai_service = AIService(auth_data)

        # 确保会话存在
        ai_service.dao.get_or_create_session(
            request.session_id,
            str(auth_data.get('user_id', 0))
        )

        # 返回流式响应
        return StreamingResponse(
            ai_service.chat_stream(
                request.session_id,
                request.message,
                request.model_type
            ),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/session/create", response_model=SessionResponseSchema, tags=["AI聊天"])
async def create_session(
        request: SessionCreateSchema,
        auth_data: dict = Depends(get_auth_data)
):
    """
    创建新的聊天会话
    """
    try:
        ai_service = AIService(auth_data)
        session_id = ai_service.create_session(request.title)

        return SessionResponseSchema(
            session_id=session_id,
            title=request.title,
            created_time=datetime.now()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/session/{session_id}/history", response_model=ChatHistoryResponseSchema, tags=["AI聊天"])
async def get_chat_history(session_id: str, auth_data: dict = Depends(get_auth_data)):
    """
    获取聊天历史记录
    """
    try:
        ai_service = AIService(auth_data)
        messages = ai_service.get_session_history(session_id)

        message_list = [
            MessageHistorySchema(
                id=msg.id,
                session_id=msg.session_id,
                type=msg.msg_type,
                content=msg.content,
                created_time=msg.created_time
            )
            for msg in messages
        ]

        return ChatHistoryResponseSchema(
            session_id=session_id,
            messages=message_list
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 保留原有的测试接口
async def generate_data():
    for i in range(10):
        yield f"Chunk {i}\n"
        await asyncio.sleep(1)


@router.post(path="/astream", tags=["测试"])
async def astream():
    """
    测试流式响应接口
    """
    return StreamingResponse(generate_data(), media_type="text/plain")
