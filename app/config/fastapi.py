
from contextlib import asynccontextmanager
from typing import Optional, Callable, Any

from fastapi import FastAPI
from pydantic import BaseModel

from app.utils.log import logger


# 配置文件：FastapiConfig
class FastapiConfig(BaseModel):
    """FastAPI应用配置类

    注意：这个类使用类属性而不是实例属性，以便与现有代码兼容
    现有代码使用 FastapiConfig.__dict__ 和 FastapiConfig.title 等方式访问
    """

    # 基础配置
    debug: bool = True
    title: str = 'FastAPI Service'
    description: str = '基于FastAPI的服务工程'
    version: str = '0.0.0.0'

    # OpenAPI配置
    openapi_url: str = '/openapi.json'
    openapi_prefix: str = ''
    docs_url: Optional[str] = None
    redoc_url: str = '/redoc'
    static_url: str = None
    # Swagger UI配置
    swagger_ui_oauth2_redirect_url: str = '/docs/oauth2-redirect'
    swagger_js_url: str = "/static/docs-ui/swagger-ui-bundle.js"
    swagger_css_url: str = "/static/docs-ui/swagger-ui.css"
    swagger_ui_init_oauth: Optional[dict] = None

    # 其他配置
    res_path: str = '../res'
    request_log_to_mysql: bool = False
    # lifespan: Callable[[FastAPI], Any] = ConfigDict(extra='ignore')

    @asynccontextmanager
    async def lifespan(app: FastAPI):
        # 启动逻辑（替代 startup 事件）
        logger.info("应用启动中...初始化资源")
        yield
        # 清理资源
        logger.info("应用停止...清理资源")


