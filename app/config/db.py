# 配置文件：DbConfig
from pydantic import BaseModel


class DbConfig(BaseModel):
    """
    DbConfig DB配置类
    :version: 1.4
    :date: 2025.07.16
    """
    driver: str = 'mysql+pymysql'
    host: str = '*************'
    port: int = 3306
    username: str = 'root'
    password: str = 'bR2Y1z40x&fAQ6e%%&NBHB^lSP#EJhHo'
    database: str = 'ai'
    charset: str = 'utf8mb4'
    table_name_prefix: str = 't_'
    echo: bool = True
    pool_size: int = 100
    max_overflow: int = 100
    pool_recycle: int = 60

    def get_url(self):
        config = [
            self.driver,
            '://',
            self.username,
            ':',
            self.password,
            '@',
            self.host,
            ':',
            str(self.port),
            '/',
            self.database,
            '?charset=',
            self.charset,
        ]

        return ''.join(config)