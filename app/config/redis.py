# 配置文件：Redis配置实例
from typing import Optional

from pydantic import BaseModel


class Node(BaseModel):
    host:str = ''
    port:int = 0

    def to_dict(self):
        return {
        "host": self.host,
        "port": self.port
        }

class Cluster(BaseModel):
    nodes: list = []
    def get_nodes(self):
        nds = []
        for n in self.nodes:
            nd = Node()
            nd.host = n.split(':')[0]
            nd.port = n.split(':')[1]
            nds.append(nd.to_dict())
        return nds

class Pool(BaseModel):
    # 连接池最大连接数（使用负值表示没有限制）
    max_connections:int = 200000
    # 连接池中的最大空闲连接
    max_idle: int = 10
    # 连接池最大阻塞等待时间（使用负值表示没有限制）
    max_wait: int = -1
    # 连接池中的最小空闲连接
    min_idle: int = 0

class RedisConfig(BaseModel):
    """
    RedisConfig Redis配置类
    用于配置Redis连接参数，包括主机地址、端口、认证信息、数据库选择和连接池设置
    Attributes:
        host (str): Redis服务器主机地址，默认为'redis'
        port (int): Redis服务器端口号，默认为6379
        username (Optional[str]): Redis用户名，用于Redis 6.0+的ACL认证，默认为'root'
        password (str): Redis密码，用于AUTH认证，默认为空字符串
        database (int): Redis数据库编号，范围0-15，默认为0
        max_connections (int): 连接池最大连接数，默认为100
    Version: 1.2
    Date: 2020-02-11
    Updated: 2024-01-01 (添加类型注解和详细注释)
    """
    # Redis服务器配置
    host: str = None  # Redis服务器主机地址
    port: int = None     # Redis服务器端口号

    # 认证配置
    username: Optional[str] = 'root'  # Redis用户名（Redis 6.0+ ACL功能）
    password: str = ''                # Redis密码（AUTH认证）

    # 数据库配置
    database: int = 0  # Redis数据库编号（0-15）

    # 连接池配置
    pool: Pool = None
    cluster: Cluster = None


