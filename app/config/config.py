import os.path

from pydantic import BaseModel

from app.config.db import DbConfig
from app.config.redis import RedisConfig
from app.utils import log
from app.config.fastapi import FastapiConfig
from app.utils.yaml import yaml_to_object

class ConfigBase(BaseModel):
    env: str = 'dev'

class ConfigModel(BaseModel):
    base: ConfigBase = None
    dbtype: str = 'mysql'
    db: DbConfig = None
    redis: RedisConfig =None
    fastapi: FastapiConfig = None

def get_config():
    try:
        log.logger.info('get_config')
        config_filename = 'config.yml'
        if not os.path.exists(config_filename): config_filename = 'config.yaml'
        config_base = yaml_to_object(config_filename, ConfigBase)

        config_filename = f'config_{config_base.env}.yml'
        if not os.path.exists(config_filename): config_filename = f'config_{config_base.env}.yaml'
        config = yaml_to_object(config_filename, ConfigModel)
        config.base = config_base
        return config

    except Exception as e:
        log.logger.error(f'配置文件解析失败: {e}')
        raise e

Config = get_config()