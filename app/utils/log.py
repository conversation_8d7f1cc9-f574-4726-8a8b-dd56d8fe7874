import logging
import os
import time
import traceback
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler

from colorlog import ColoredFormatter


class Filter(logging.Filter):
    def __init__(self, name, level=logging.DEBUG):
        super().__init__(name=name)
        self.level = level

    def filter(self, record):
        # 仅允许记录级别小于或等于WARNING的日志
        return record.levelno == self.level



class TimeLoggerRolloverHandler(TimedRotatingFileHandler):
    def __init__(self, filename, when='h', base_path='logs', path_format='%Y-%m', interval=1, backupCount=0,
                 encoding=None, delay=False, utc=False, atTime=None):
        self.base_path = base_path
        self.path_format = path_format
        self.file_name = filename
        super(TimeLoggerRolloverHandler, self).__init__(filename, when, interval, backupCount, encoding, delay, utc, atTime=atTime)
        # 删除已创建的原始文件
        if os.path.exists(filename):
            if self.stream:
                self.stream.close()
                self.stream = None
            os.remove(filename)
        # 执行一次
        self.doRollover()

    def doRollover(self):
        """
        TimedRotatingFileHandler对日志的切分是在满足设定的时间间隔后，执行doRollover方法，
        将my.log重命名为带有当前时间后缀(my.log.****)的文件，并新建一个my.log，继续记录后续日志。
        (1) 重写TimedRotatingFileHandler的doRollover方法的文件翻转块代码
        做了以下两点改动：
            重定义了新文件名，将日期放在了中间而不是最后
            直接将将baseFilename 指向新文件
        """
        try:
            if self.stream:
                self.stream.close()
                self.stream = None
            currentTime = int(time.time())
            dstNow = time.localtime(currentTime)[-1]
            # log_type = 'info' if self.level == 20 else 'error'
            # 重新定义了新文件名
            self.base_path = self.base_path if self.base_path is not None else ''
            base_dir = os.path.abspath(self.base_path)
            datetime_now = datetime.now()
            self.path_format = self.path_format.replace('/', os.sep).replace('\\', os.sep)
            self.path_format = self.path_format.strip(os.sep)
            log_dir = f'{base_dir}{os.sep}{datetime_now.strftime(self.path_format)}'
            if not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            # dfn = f"{log_dir}{os.sep}{os.path.basename(self.baseFilename)}"
            log_filename = f"{datetime_now.strftime('%Y%m%d_%H%M%S')}"
            if self.when == 'H':
                log_filename = f"{datetime_now.strftime('%Y%m%d%H')}"
            elif self.when == 'M':
                log_filename = f"{datetime_now.strftime('%Y%m%d%H%M')}"
            elif self.when == 'S':
                log_filename = f"{datetime_now.strftime('%Y%m%d%H%M%S')}"
            elif self.when == 'D':
                log_filename = f"{datetime_now.strftime('%Y%m%d')}"

            dfn = f"{log_dir}{os.sep}{log_filename}_{self.file_name}"

            self.baseFilename = dfn  # 直接将将baseFilename 指向新文件

            # if os.path.exists(dfn):
            #     os.remove(dfn)
            # self.rotate(self.baseFilename, dfn)
            # if self.backupCount > 0:
            #     for s in self.getFilesToDelete():
            #         os.remove(s)
            if not self.delay:
                self.stream = self._open()
            newRolloverAt = self.computeRollover(currentTime)
            while newRolloverAt <= currentTime:
                newRolloverAt = newRolloverAt + self.interval
            # If DST changes and midnight or weekly rollover, adjust for this.
            if (self.when == 'MIDNIGHT' or self.when.startswith('W')) and not self.utc:
                dstAtRollover = time.localtime(newRolloverAt)[-1]
                if dstNow != dstAtRollover:
                    if not dstNow:  # DST kicks in before next rollover, so we need to deduct an hour
                        addend = -3600
                    else:  # DST bows out before next rollover, so we need to add an hour
                        addend = 3600
                    newRolloverAt += addend
            self.rolloverAt = newRolloverAt
        except Exception as e:
            print(traceback.print_stack())
            print(e)


logger = logging.getLogger('logger')
logger.setLevel(logging.DEBUG)
# 创建一个控制台handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
# 创建一个文件handler
file_handler_info = TimeLoggerRolloverHandler(filename='INFO.log', when='D', path_format='%Y%m', base_path='logs', encoding='utf-8')
file_handler_info.setLevel(logging.INFO)
file_handler_info.addFilter(Filter('INFO',logging.INFO))
# file_handler_debug = TimeLoggerRolloverHandler(filename='DEBUG.log', when='D', path_format='%Y%m', base_path='logs', encoding='utf-8')
# file_handler_debug.setLevel(logging.DEBUG)
# file_handler_debug.addFilter(Filter('DEBUG',logging.DEBUG))
file_handler_warning = TimeLoggerRolloverHandler(filename='WARN.log', when='D', path_format='%Y%m', base_path='logs', encoding='utf-8')
file_handler_warning.setLevel(logging.WARNING)
file_handler_warning.addFilter(Filter('WARNING',logging.WARNING))
file_handler_error = TimeLoggerRolloverHandler(filename='ERROR.log', when='D', path_format='%Y%m', base_path='logs', encoding='utf-8')
file_handler_error.setLevel(logging.ERROR)
file_handler_error.addFilter(Filter('ERROR',logging.ERROR))
file_handler_critical = TimeLoggerRolloverHandler(filename='CRITICAL.log', when='D', path_format='%Y%m', base_path='logs', encoding='utf-8')
file_handler_critical.setLevel(logging.CRITICAL)
file_handler_critical.addFilter(Filter('CRITICAL',logging.CRITICAL))
# file_handler = logging.handlers.TimedRotatingFileHandler(filename='logs\log.log',when='D')


# 设置日志颜色配置
log_colors_config = {
    'DEBUG': 'blue',
    'INFO': 'green',
    'WARNING': 'yellow',
    'ERROR': 'red',
    'CRITICAL': 'bold_purple',
}

# 创建一个彩色日志格式器
console_formatter = ColoredFormatter(
    fmt='%(log_color)s[%(asctime)s.%(msecs)03d] %(filename)s->%(funcName)s line:%(lineno)d [%(levelname)s] : %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    log_colors=log_colors_config
)
file_formatter = logging.Formatter(
    fmt='[%(asctime)s.%(msecs)03d] %(filename)s->%(funcName)s line:%(lineno)d [%(levelname)s] : %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 将格式器设置到控制台handler
logger.addHandler(console_handler)
# 设置文件日志handler
console_handler.setFormatter(console_formatter)
for handler in [file_handler_info,file_handler_error,file_handler_warning,file_handler_critical]:
    handler.setFormatter(file_formatter)
    logger.addHandler(handler)

