import sqlalchemy
from sqlalchemy import create_engine, Column
from sqlalchemy.orm import sessionmaker, scoped_session, Session, InstrumentedAttribute

from app.config.config import Config
from app.config.db import DbConfig


class DbUtils(object):
    """
    DbUtils DB工具类
    :version: 1.4
    :date: 2020-02-11
    """

    sess: Session = None
    default_config: DbConfig = Config.db

    def __init__(self, config: DbConfig = None):
        if not config:
            config = self.default_config

        self.sess = self._create_scoped_session(config)

    def __del__(self):
        if self.sess:
            self.sess.close()

    @staticmethod
    def _create_scoped_session(config: DbConfig):
        engine = create_engine(
            config.get_url(),
            pool_size=config.pool_size,
            max_overflow=config.max_overflow,
            pool_recycle=config.pool_recycle,
            echo=config.echo
        )

        session_factory = sessionmaker(autoflush=True, bind=engine)

        return scoped_session(session_factory)

    # 根据文件获取SQL文件
    @staticmethod
    def get_sql_by_file(file_path, params={}):
        sql = DbUtils._get_file(file_path)
        return sql.format(**params)

    # 获取SQL文件
    @staticmethod
    def _get_file(file_path):
        with open('app/sql/' + file_path, 'r', encoding='utf-8') as f:
            return f.read()

    @staticmethod
    def get_all_fields(cls):
        """递归获取类及其所有基类的属性（不包括方法）"""
        attributes = {}
        # 获取当前类的属性
        for attr, value in cls.__dict__.items():
            # 过滤方法和魔法属性
            if type(value) is Column or type(value) is InstrumentedAttribute:
                attributes[attr] = value
            # if not attr.startswith("__") and not callable(value):
            #     attributes[attr] = value
        # 递归处理基类
        for base in cls.__bases__:
            base_attrs = DbUtils.get_all_fields(base)
            attributes.update(base_attrs)  # 基类属性会被子类同名属性覆盖
        return attributes

    @staticmethod
    def set_model_value(target, source, can_setNull=True):
        if type(target) is not type(source):
            raise TypeError(f'target:{type(target)}与source:{type(source)}类型不一致！')

        attrs = DbUtils.get_all_fields(type(source))
        for attr in attrs:
            if hasattr(target, attr):
                value = getattr(source, attr)
                if can_setNull or value is not None:
                    setattr(target, attr, value)

