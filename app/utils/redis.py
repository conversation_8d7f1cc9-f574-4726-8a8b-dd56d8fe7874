import json
from typing import Optional

from redis import ConnectionPool, Redis, RedisCluster



from .log import logger
from ..config.config import Config
from ..config.redis import RedisConfig


class RedisUtils:
    """
    RedisUtils redis工具类
    :version: 1.2
    :date: 2020-02-11
    """

    _conn = None
    _default_conn_pool = None
    default_config: RedisConfig = Config.redis

    def __init__(self, config: RedisConfig = None):
        if not config:
            config = self.default_config
        if config:
            self._conn = self._get_conn(config)
        else:
            if not self._default_conn_pool:
                RedisUtils._default_conn_pool = self._create_pool(self.default_config)

            self._conn = Redis(connection_pool=self._default_conn_pool)

    @staticmethod
    def _create_pool(config: RedisConfig):
        return ConnectionPool(
            host=config.host,
            port=config.port,
            max_connections=config.pool.max_connections,
            username=config.username,
            password=config.password,
            db=config.database
        )

    @staticmethod
    def _get_conn(config: RedisConfig):
        print(111)
        if config.cluster: # 使用Redis集群
            rc = RedisCluster(
                host=config.host,
                port=config.port,
                # startup_nodes=config.cluster.get_nodes(),
                decode_responses=True,  # 自动解码为字符串（默认返回bytes）
                password=config.password,
                # db=config.database,
                skip_full_coverage_check=True  # 跳过集群完整性检查（开发环境可用，生产环境建议关闭）
            )
            return rc
        else:# 使用单台Redis服务器
            return Redis(host=config.host,
                         port=config.port,
                         max_connections=config.pool.max_connections,
                         username=config.username,
                         password=config.password,
                         db=config.database)

    def delete(self, key):
        return self._conn.delete(key)



    def set(self, key, value, ex=None):
        return self._conn.set(key, value, ex)

    def get(self, key):
        return self._conn.get(key)

    def expire(self, key, ex=int):
        return self._conn.expire(key, ex)
