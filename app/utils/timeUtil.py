import datetime


def str_to_timestamp(time_str, format_str="%Y-%m-%d %H:%M:%S"):
    """
    将时间字符串转换为时间戳
    :param time_str: 时间字符串，如"2023-01-01 12:00:00"
    :param format_str: 时间字符串的格式
    :return: 时间戳（秒）
    """
    try:
        # 将字符串转换为datetime对象
        dt = datetime.datetime.strptime(time_str, format_str)
        # 转换为时间戳
        timestamp = dt.timestamp()
        return timestamp
    except ValueError as e:
        print(f"转换失败: {e}")
        return None