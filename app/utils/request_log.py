import json
import time
import traceback
from typing import Optional

from starlette.requests import Request
from starlette.responses import Response

from .auth import get_auth_data_by_authorization, get_auth_data_by_token
from .convert import list_list2dict
from .db import DbUtils

from .log import logger
from .mongo import MongoUtils

from ..config.config import Config
from ..models.request_log import RequestLog
from ..core.dao.request import RequestDao


async def create_log(request: Request) -> Optional[RequestLog]:
    if not Config.fastapi.request_log_to_mysql:
        return None
    log = RequestLog()
    log.status = 1
    log.ip = request.client.host
    log.url = str(request.url)
    log.method = request.method
    log.path = request.url.path
    log.path_params = str(request.path_params)
    log.query_params = str(request.url.query)
    log.header = list_list2dict(request.headers.items())

    try:
        log.body = str(await request.json())
    except Exception as e:
        logger.warning(traceback.format_exc())

    # if 'authorization' in log.header:
    #     auth_data = get_auth_data_by_authorization(log.header['authorization'])
    #
    #     if auth_data:
    #         log.user_id = auth_data.get('user_id')
    log.header = str(log.header)
    dao = RequestDao()
    dao.create(log)
    request.state.log = log
    return log


async def update_log(log: RequestLog, response: Response):
    dao = RequestDao()
    if not log or not Config.fastapi.request_log_to_mysql:
        return
    log.status = 2
    log.response_status_code = response.status_code

    try:
        log.response = json.loads(str(response.body, 'utf8'))
    except:
        logger.warning(traceback.format_exc())

    if not log.user_id and log.response and 'token' in log.response:
        # auth_data = get_auth_data_by_token(log.response['token'])
        # log.user_id = auth_data.get('user_id')
        pass
    log.response = str(log.response)
    dao.update(log)
