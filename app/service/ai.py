import asyncio
import uuid
import json
from typing import AsyncGenerator, List
from datetime import datetime

from langchain_community.llms import Tongyi
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

from app.config.config import Config
from app.core.dao.ai.message import MessageDao
from app.models.ai.message import Message, MsgType, Session
from app.schema.ai import ModelType, ChatMessageSchema
import app.config.db


class AIService:
    def __init__(self, auth_data: dict = {}):
        self.user_id = auth_data.get('user_id', 0)
        self.auth_data = auth_data
        self.dao = MessageDao(config=Config.db)

    def _get_model(self, model_type: ModelType):
        """
        根据模型类型获取对应的模型实例
        """
        if model_type == ModelType.TONGYI:
            # 需要设置环境变量 DASHSCOPE_API_KEY
            return Tongyi()
        elif model_type == ModelType.OPENAI:
            # 这里可以添加OpenAI模型
            # from langchain_openai import ChatOpenAI
            # return ChatOpenAI()
            raise NotImplementedError("OpenAI model not implemented yet")
        elif model_type == ModelType.CLAUDE:
            # 这里可以添加Claude模型
            raise NotImplementedError("Claude model not implemented yet")
        elif model_type == ModelType.GEMINI:
            # 这里可以添加Gemini模型
            raise NotImplementedError("Gemini model not implemented yet")
        elif model_type == ModelType.BAIDU:
            # 这里可以添加百度文心一言模型
            raise NotImplementedError("Baidu model not implemented yet")
        else:
            raise ValueError(f"Unsupported model type: {model_type}")

    def _convert_messages_to_langchain(self, messages: List[Message]) -> List:
        """
        将数据库中的消息转换为LangChain格式
        """
        langchain_messages = []
        for msg in messages:
            if msg.msg_type == MsgType.SYS:
                langchain_messages.append(SystemMessage(content=msg.content))
            elif msg.msg_type == MsgType.USER:
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.msg_type == MsgType.ASSISTANT:
                langchain_messages.append(AIMessage(content=msg.content))
        return langchain_messages

    def save_user_message(self, session_id: str, content: str) -> Message:
        """
        保存用户消息到数据库
        """
        message = Message()
        message.session_id = session_id
        message.msg_type = MsgType.USER
        message.content = content
        message.user_id = str(self.user_id)
        return self.dao.create_message(message)

    def save_assistant_message(self, session_id: str, content: str, model_type: str, tokens_used: str = None) -> Message:
        """
        保存AI助手消息到数据库
        """
        message = Message()
        message.session_id = session_id
        message.msg_type = MsgType.ASSISTANT
        message.content = content
        message.model_type = model_type
        message.tokens_used = tokens_used
        message.user_id = str(self.user_id)
        return self.dao.create_message(message)

    def get_session_history(self, session_id: str) -> List[Message]:
        """
        获取会话历史消息
        """
        return self.dao.get_session_messages(session_id)

    def create_session(self, title: str = "新对话") -> str:
        """
        创建新会话
        """
        session_id = str(uuid.uuid4())
        self.dao.get_or_create_session(session_id, str(self.user_id), title)
        return session_id

    async def chat_stream(self, session_id: str, user_message: str, model_type: ModelType = ModelType.TONGYI) -> AsyncGenerator[str, None]:
        """
        流式聊天生成器
        """
        try:
            # 1. 保存用户消息
            user_msg = self.save_user_message(session_id, user_message)

            # 2. 获取历史消息
            history_messages = self.get_session_history(session_id)

            # 3. 转换为LangChain格式
            langchain_messages = self._convert_messages_to_langchain(history_messages)

            # 4. 添加当前用户消息
            langchain_messages.append(HumanMessage(content=user_message))

            # 5. 获取AI模型
            model = self._get_model(model_type)

            # 6. 流式生成回复
            assistant_content = ""
            async for chunk in model.astream(langchain_messages):
                chunk_content = str(chunk)
                assistant_content += chunk_content

                # 返回流式数据，格式为SSE
                yield f"data: {json.dumps({'content': chunk_content, 'type': 'chunk'}, ensure_ascii=False)}\n\n"

            # 7. 保存AI回复消息
            assistant_msg = self.save_assistant_message(
                session_id,
                assistant_content,
                model_type.value
            )

            # 8. 发送完成信号
            yield f"data: {json.dumps({'content': '', 'type': 'done', 'message_id': assistant_msg.id}, ensure_ascii=False)}\n\n"

        except Exception as e:
            # 发送错误信息
            error_msg = f"Error: {str(e)}"
            yield f"data: {json.dumps({'content': error_msg, 'type': 'error'}, ensure_ascii=False)}\n\n"