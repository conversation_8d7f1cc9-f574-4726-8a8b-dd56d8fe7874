import hashlib
import uuid

from app.schema.base import RespIdSchema
from app.service.base import BaseService
from app.utils.convert import obj2json

from app.utils.redis import RedisUtils

from app.core.dao.user import UserDao
from app.models.user import User


class UserService(BaseService):
    def __init__(self, auth_data: dict = None):
        auth_data = dict() if not auth_data else auth_data
        user_id = auth_data.get('user_id', 0)
        self.Model = User
        self.dao = UserDao()
        self.dao.Model = User
        self.redis = RedisUtils()
        # self.wxapp = WxappUtils()

        super().__init__(user_id, auth_data)

    def create(self, user):
        if not user:
            return {'code': 20000, 'message': '用户信息不能为空'}
        elif not user.login_name:
            return {'code': 20001, 'message': '登录名不能为空'}
        elif not user.username:
            return {'code': 20002, 'message': '用户名不能为空'}
        elif not user.password:
            return {'code': 20003, 'message': '密码不能为空'}
        u = self.dao.read_by_login_name(user.login_name)
        if u:
            return {'code': 20004, 'message': f'登录名[{u.login_name}]已存在'}
        user.password=hashlib.md5((user.login_name + user.password).encode(encoding='UTF-8')).hexdigest()
        return super().create(user)

    def get_by_id(self,id):
        return super().read(id)

    def login_by_code(self, code: str):
        """
        通过微信登录code进行登录
        :param code:
        :return:
        """

        # data = self.wxapp.jscode2session(code)
        #
        # if 'openid' not in data:
        #     return {
        #         'code': 500,
        #         'message': '错误：jscode2session'
        #     }
        #
        # return self.login_by_openid(data['openid'])
        pass

    def login_by_token(self, token: str):
        """
        通过自产token进行登录
        :param token:
        :return:
        """

        data = self.redis.get('token:' + token)

        if not data:
            return {
                'code': 500,
                'message': '错误：token错误'
            }

        # 删除当前token，因为会重新创建
        self.redis.delete('token:' + token)

        return self._login_by_id(data['user']['id'])

    def login_by_password(self, login_name: str, password: str):
        """
        通过username、password进行登录
        :param username:
        :param password:
        :return:
        """
        user = self.dao.read_by_login_name(login_name)
        if not user:
            return {'code': 20010, 'message': '用户不存在'}
        if user.password != hashlib.md5((user.login_name + password).encode(encoding='UTF-8')).hexdigest():
            return {'code': 20011, 'message': '密码错误'}
        return self._login_success(user)

    def login_by_openid(self, openid: str):
        """
        通过openid进行登录
        :param openid:
        :return:
        """
        # user = self.dao.read_by_openid(openid)
        user = None

        if not user:
            user = User()
            user.openid = openid
            self.dao.create(user)

        return self._login_success(user)

    def _login_success(self, user):
        token = str(user.id) + str(uuid.uuid1())

        self.redis.set('token:' + token, obj2json(user), 360000)

        return {'token':token,"user":user}

    def _login_by_id(self, id: int):
        """
        通过id进行登录
        :param openid:
        :return:
        """

        user = self.dao.read(id)

        if not user: return {'code': 2008061621, 'message': '用户不存在'}

        return self._login_success(user)


