from app.core.dao.base import BaseDao

from app.models.user import User


class UserDao(BaseDao):
    Model = User
    def read(self, id: int, is_deleted: int = 0) -> User:
        return super().read(id) 

    # def read_by_openid(self, openid: str) -> User:
    #     return self.db.sess.query(User).filter(
    #         User.openid == openid,
    #         User.is_deleted == 0,
    #     ).first()

    def read_by_login_name(self, login_name: str) -> User:
        return self.db.sess.query(User).filter(
            User.login_name == login_name,
            User.is_deleted == 0,
        ).first()
