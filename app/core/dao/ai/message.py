from typing import List
from sqlalchemy import and_, desc

from app.core.dao.base import BaseDao
from app.models.ai.message import Message, Session


class MessageDao(BaseDao):

    def __init__(self, config):
        super().__init__(config=config)

    def get_session_messages(self, session_id: str, limit: int = 50) -> List[Message]:
        """
        获取会话的历史消息
        :param session_id: 会话ID
        :param limit: 限制条数
        :return: 消息列表
        """
        return self.db.sess.query(Message).filter(
            Message.session_id == session_id
        ).order_by(Message.created_time.asc()).limit(limit).all()

    def create_message(self, message: Message) -> Message:
        """
        创建消息
        :param message: 消息对象
        :return: 创建的消息对象
        """
        self.db.sess.add(message)
        self.db.sess.commit()
        self.db.sess.refresh(message)
        return message

    def get_or_create_session(self, session_id: str, user_id: str, title: str = "新对话") -> Session:
        """
        获取或创建会话
        :param session_id: 会话ID
        :param user_id: 用户ID
        :param title: 会话标题
        :return: 会话对象
        """
        session = self.db.sess.query(Session).filter(
            Session.session_id == session_id
        ).first()

        if not session:
            session = Session()
            session.session_id = session_id
            session.user_id = user_id
            session.title = title
            self.db.sess.add(session)
            self.db.sess.commit()
            self.db.sess.refresh(session)

        return session