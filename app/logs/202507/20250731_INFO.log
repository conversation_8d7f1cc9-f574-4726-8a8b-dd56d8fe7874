[2025-07-31 17:00:49.957] request_log.py->create_log line:38 [INFO] : <starlette.middleware.base._CachedRequest object at 0x0000024EC103A660>
[2025-07-31 17:03:26.970] request_log.py->create_log line:38 [INFO] : <starlette.middleware.base._CachedRequest object at 0x000001FE9D5A6660>
[2025-07-31 17:03:26.970] request_log.py->update_log line:45 [INFO] : <starlette.responses.Response object at 0x000001FE9D5A7CB0>
[2025-07-31 17:04:57.434] request_log.py->create_log line:38 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 58288), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/astream', 'raw_path': b'/v1/ai/astream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'0'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x0000016E2336E660>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000016E234963C0>>, '_send': <function empty_send at 0x0000016E204F2C00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x0000016E234C9540>, '_url': URL('http://127.0.0.1:8001/v1/ai/astream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '0', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-07-31 17:04:57.444] request_log.py->update_log line:45 [INFO] : {'status_code': 401, 'background': None, 'body': b'{"code": 401, "message": "Unauthorized"}', 'raw_headers': [(b'content-length', b'40')]}
[2025-07-31 17:40:53.414] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 53203), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000002789E06E7B0>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002789E25C510>>, '_send': <function empty_send at 0x000002789B282C00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000002789E25B920>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-07-31 17:52:17.281] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-07-31 17:52:17.281] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-07-31 17:52:17.281] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-07-31 17:52:17.281] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-07-31 17:52:17.281] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-07-31 17:52:24.858] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 53815), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000002D6B7748050>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002D6B774BE00>>, '_send': <function empty_send at 0x000002D6B46C2C00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000002D6B7766500>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-07-31 17:53:19.272] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-07-31 17:53:19.272] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-07-31 17:53:19.273] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-07-31 17:53:19.273] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-07-31 17:53:19.273] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-07-31 17:53:23.967] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 53865), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x00000275C5DF8050>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000275C5DFBE00>>, '_send': <function empty_send at 0x00000275C2D1EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x00000275C5E16500>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-07-31 17:54:59.260] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-07-31 17:54:59.261] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-07-31 17:54:59.261] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-07-31 17:54:59.261] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-07-31 17:54:59.261] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-07-31 17:55:03.235] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 53946), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000002525FA0BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002525FC17CB0>>, '_send': <function empty_send at 0x000002525CBB2C00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000002525FC36500>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-07-31 17:55:51.414] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-07-31 17:55:51.415] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-07-31 17:55:51.415] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-07-31 17:55:51.415] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-07-31 17:55:51.415] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-07-31 17:55:54.783] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 53981), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000002D6210DBE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002D6212EBCB0>>, '_send': <function empty_send at 0x000002D61E232C00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000002D621306500>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-07-31 17:55:54.784] request_log.py->create_log line:21 [INFO] : True
[2025-07-31 17:57:36.803] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-07-31 17:57:36.804] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-07-31 17:57:36.805] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-07-31 17:57:36.806] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-07-31 17:57:36.806] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-07-31 17:57:40.181] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 54026), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001D8BA89BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001D8BAAABCB0>>, '_send': <function empty_send at 0x000001D8B7A22C00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001D8BAAC6500>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-07-31 17:57:40.181] request_log.py->create_log line:21 [INFO] : True
