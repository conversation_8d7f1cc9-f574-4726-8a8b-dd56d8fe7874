[2025-08-01 11:36:36.269] request_log.py->create_log line:38 [WARNING] : ['  File "D:\\Code\\Git\\xxgitea\\AIChatService\\app\\main.py", line 113, in <module>\n    uvicorn.run(app, host="127.0.0.1", port=8001)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\uvicorn\\main.py", line 580, in run\n    server.run()\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\uvicorn\\server.py", line 67, in run\n    return asyncio.run(self.serve(sockets=sockets))\n', '  File "D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\runners.py", line 195, in run\n    return runner.run(main)\n', '  File "D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\runners.py", line 118, in run\n    return self._loop.run_until_complete(task)\n', '  File "D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\base_events.py", line 712, in run_until_complete\n    self.run_forever()\n', '  File "D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\base_events.py", line 683, in run_forever\n    self._run_once()\n', '  File "D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\base_events.py", line 2042, in _run_once\n    handle._run()\n', '  File "D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\events.py", line 89, in _run\n    self._context.run(self._callback, *self._args)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py", line 403, in run_asgi\n    result = await app(  # type: ignore[func-returns-value]\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\uvicorn\\middleware\\proxy_headers.py", line 60, in __call__\n    return await self.app(scope, receive, send)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\fastapi\\applications.py", line 1054, in __call__\n    await super().__call__(scope, receive, send)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\starlette\\applications.py", line 112, in __call__\n    await self.middleware_stack(scope, receive, send)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\starlette\\middleware\\errors.py", line 165, in __call__\n    await self.app(scope, receive, _send)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\starlette\\middleware\\cors.py", line 93, in __call__\n    await self.simple_response(scope, receive, send, request_headers=headers)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\starlette\\middleware\\cors.py", line 144, in simple_response\n    await self.app(scope, receive, send)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\.venv\\Lib\\site-packages\\starlette\\middleware\\base.py", line 178, in __call__\n    response = await self.dispatch_func(request, call_next)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\app\\main.py", line 75, in auth_token\n    log = await create_log(req)\n', '  File "D:\\Code\\Git\\xxgitea\\AIChatService\\app\\utils\\request_log.py", line 38, in create_log\n    logger.warning(traceback.format_stack())\n']
[2025-08-01 11:39:51.410] request_log.py->create_log line:38 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 36, in create_log
    log.body = await str(request.json())
               ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: object str can't be used in 'await' expression

[2025-08-01 11:59:13.014] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-01 12:00:34.129] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-01 12:00:36.454] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

