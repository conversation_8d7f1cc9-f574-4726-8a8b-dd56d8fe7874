[2025-08-06 10:26:01.595] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 10:26:30.038] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 17:16:40.513] request_log.py->create_log line:37 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 35, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 17:16:51.632] request_log.py->create_log line:37 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 35, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 17:17:00.089] request_log.py->create_log line:37 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 35, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 17:17:05.917] request_log.py->create_log line:37 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 35, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

