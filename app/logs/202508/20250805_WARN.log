[2025-08-05 13:42:31.393] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 13:42:49.405] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 13:42:53.033] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 13:49:26.279] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 13:51:34.237] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 13:51:38.091] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 13:53:32.650] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:03:32.735] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:04:13.141] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:04:25.022] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:18:01.385] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:18:01.502] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:18:02.949] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:18:07.150] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:18:07.291] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:20:41.435] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:20:41.558] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:20:41.674] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:20:41.837] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:22:35.922] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:22:51.377] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:22:51.477] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:22:56.142] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:22:56.518] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:29:24.255] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:43:28.285] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 14:46:00.399] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 15:39:23.291] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 15:39:35.423] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 15:44:05.900] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:05:16.758] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:09:34.590] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:18:53.447] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:19:05.081] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:19:36.658] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:24:33.159] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:24:43.755] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:26:19.772] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:27:26.096] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:27:29.733] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:29:19.446] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:29:32.285] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:29:34.327] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:29:34.983] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:29:50.785] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:31:04.829] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:33:41.645] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:43:06.701] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-05 16:43:35.388] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 10:23:18.663] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 10:24:57.563] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 10:25:00.541] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 10:25:31.903] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 10:25:34.929] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 10:25:42.930] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[2025-08-06 10:25:44.752] request_log.py->create_log line:36 [WARNING] : Traceback (most recent call last):
  File "D:\Code\Git\xxgitea\AIChatService\app\utils\request_log.py", line 34, in create_log
    log.body = str(await request.json())
                   ^^^^^^^^^^^^^^^^^^^^
  File "D:\Code\Git\xxgitea\AIChatService\.venv\Lib\site-packages\starlette\requests.py", line 249, in json
    self._json = json.loads(body)
                 ~~~~~~~~~~^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\ENVS\Python\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

