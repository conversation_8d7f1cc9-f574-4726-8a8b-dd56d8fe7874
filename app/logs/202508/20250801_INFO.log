[2025-08-01 10:33:02.711] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:33:02.712] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:33:02.712] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:33:02.712] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:33:02.712] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:33:24.205] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 50151), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001CB2458BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001CB247A35C0>>, '_send': <function empty_send at 0x000001CB217FEC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001CB2483D460>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:33:24.214] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:37:19.706] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:37:19.706] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:37:19.706] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:37:19.706] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:37:19.706] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:37:39.539] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:37:39.540] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:37:39.540] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:37:39.540] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:37:39.540] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:42:33.387] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 61387), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001D41FA9BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001D41FCA7CB0>>, '_send': <function empty_send at 0x000001D41CC9EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001D420CA2420>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:42:33.388] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:42:33.392] request_log.py->create_log line:48 [INFO] : <app.models.request_log.RequestLog object at 0x000001D420CE16A0>
[2025-08-01 10:42:33.398] request_log.py->update_log line:68 [INFO] : <app.models.request_log.RequestLog object at 0x000001D420CE16A0>
[2025-08-01 10:43:14.680] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:43:14.680] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:43:14.680] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:43:14.681] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:43:14.681] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:43:19.940] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 57340), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x0000021961E2BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000021962037CB0>>, '_send': <function empty_send at 0x000002195F02EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x0000021962062420>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:43:19.942] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:43:19.948] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x0000021962066210>, 'status': 1, 'ip': '127.0.0.1', 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'method': 'POST', 'path': '/v1/ai/chat/stream', 'path_params': {}, 'query_params': '', 'header': {'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}, 'body': {'session_id': 'string', 'message': 'string', 'model_type': 'tongyi', 'temperature': 0.7, 'max_tokens': 2000, 'stream': True}}
[2025-08-01 10:43:19.949] request_log.py->update_log line:68 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x0000021962066210>, 'status': 2, 'ip': '127.0.0.1', 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'method': 'POST', 'path': '/v1/ai/chat/stream', 'path_params': {}, 'query_params': '', 'header': {'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'}, 'body': {'session_id': 'string', 'message': 'string', 'model_type': 'tongyi', 'temperature': 0.7, 'max_tokens': 2000, 'stream': True}, 'response_status_code': 401, 'response': {'code': 401, 'message': 'Unauthorized'}}
[2025-08-01 10:46:45.017] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:46:45.018] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:46:45.018] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:46:45.018] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:46:45.018] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:46:48.056] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 57454), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x00000292C713BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000292C734BCB0>>, '_send': <function empty_send at 0x00000292C433EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x00000292C8342420>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:46:48.057] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:47:45.117] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:47:45.118] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:47:45.118] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:47:45.119] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:47:45.120] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:47:51.251] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 57518), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001B6A3087380>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001B6A328AF90>>, '_send': <function empty_send at 0x000001B6A02D16C0>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001B6A32A2CE0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:47:51.252] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:48:34.400] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:48:34.400] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:48:34.400] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:48:34.401] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:48:34.401] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:48:38.002] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 57536), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x0000021DE5B9BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000021DE5DABCB0>>, '_send': <function empty_send at 0x0000021DE2E4EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x0000021DE6DA2420>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:48:38.002] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:48:38.086] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x0000021DE6DA62D0>, 'status': 1}
[2025-08-01 10:51:14.589] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:51:14.590] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:51:14.591] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:51:14.591] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:51:14.592] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:52:19.171] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 57628), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001A2CAA02CF0>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001A2CABFE900>>, '_send': <function empty_send at 0x000001A2C7C716C0>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001A2CAC0EA40>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:52:19.172] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:52:19.293] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000001A2CAC16AB0>, 'status': 1}
[2025-08-01 10:53:32.895] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:53:32.896] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:53:32.897] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:53:32.897] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:53:32.898] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:53:40.510] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 57661), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x0000021DD2AB2CF0>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000021DD2CAE900>>, '_send': <function empty_send at 0x0000021DCFCD16C0>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x0000021DD2CBEA40>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:53:40.511] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:53:40.631] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x0000021DD2CC69F0>, 'status': 1}
[2025-08-01 10:56:06.492] request_log.py->update_log line:68 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x0000021DD2CC69F0>, 'status': 2, 'query_params': '', 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'path': '/v1/ai/chat/stream', 'body': None, 'user_id': '', 'response': {'code': 401, 'message': 'Unauthorized'}, 'created_time': datetime.datetime(2025, 8, 1, 10, 53, 39), 'response_status_code': 401, 'updated_time': datetime.datetime(2025, 8, 1, 10, 53, 39), 'path_params': '{}', 'id': 3, 'method': 'POST', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'ip': '127.0.0.1'}
[2025-08-01 10:56:36.464] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 10:56:36.464] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 10:56:36.464] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 10:56:36.464] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 10:56:36.464] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 10:56:46.234] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 57718), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001EB1FC4BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001EB20E9D450>>, '_send': <function empty_send at 0x000001EB1CE3EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001EB1FE76260>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:56:46.235] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:56:46.277] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000001EB20EB4C50>, 'status': 1}
[2025-08-01 10:57:01.190] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 57727), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001EB1FC4BE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001EB21160050>>, '_send': <function empty_send at 0x000001EB1CE3EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001EB20F110E0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 10:57:01.191] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 10:57:01.256] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000001EB21107710>, 'status': 1}
[2025-08-01 11:09:07.031] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:09:07.031] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:09:07.031] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:09:07.031] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:09:07.031] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:09:14.124] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 52099), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000002796F5ABE00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002796F7BBCB0>>, '_send': <function empty_send at 0x000002796C79EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000002796F7E2420>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:09:14.125] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:09:14.175] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000002796F7E6270>, 'status': 1}
[2025-08-01 11:09:30.496] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:09:30.497] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:09:30.497] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:09:30.498] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:09:30.498] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:09:33.836] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 52113), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000002C8A56A2CF0>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002C8A589E900>>, '_send': <function empty_send at 0x000002C8A29416C0>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000002C8A58AEA40>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:09:33.837] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:09:33.956] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000002C8A58B6BD0>, 'status': 1}
[2025-08-01 11:13:42.838] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:13:42.839] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:13:42.839] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:13:42.839] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:13:42.839] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:13:54.847] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 52231), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000002F5E1583E00>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000002F5E179BCB0>>, '_send': <function empty_send at 0x000002F5DE77EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000002F5E2796420>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:13:54.847] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:13:54.899] request_log.py->create_log line:48 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000002F5E279A270>, 'status': 1, 'method': 'POST', 'user_id': '', 'path_params': '{}', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'response_status_code': None, 'id': 8, 'updated_time': datetime.datetime(2025, 8, 1, 11, 13, 53), 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'path': '/v1/ai/chat/stream', 'ip': '127.0.0.1', 'query_params': '', 'body': None, 'response': None, 'created_time': datetime.datetime(2025, 8, 1, 11, 13, 53)}
[2025-08-01 11:17:44.439] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:17:44.439] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:17:44.439] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:17:44.439] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:17:44.439] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:17:51.954] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 52352), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x0000015033DA41A0>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000015033DA7E00>>, '_send': <function empty_send at 0x0000015030D8EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x0000015033DC34C0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:17:51.954] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:18:06.784] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 52367), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x0000015033DA41A0>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000015033E1B9D0>>, '_send': <function empty_send at 0x0000015030D8EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x0000015033E86A40>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:18:06.784] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:28:26.706] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:28:26.707] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:28:26.707] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:28:26.707] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:28:26.707] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:28:36.127] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 65484), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001B6097E4050>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001B6097E7E00>>, '_send': <function empty_send at 0x000001B60582EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001B6097FB220>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:28:36.127] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:28:45.014] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:28:45.015] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:28:45.015] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:28:45.016] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:28:45.017] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:28:51.042] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 65496), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x00000224B52B2E40>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000224B54AEA50>>, '_send': <function empty_send at 0x00000224B24F16C0>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x00000224B54B7840>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:28:51.043] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:29:15.675] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 65504), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x00000224B52B2E40>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000224B55920D0>>, '_send': <function empty_send at 0x00000224B24F16C0>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x00000224B55A7D80>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:29:16.286] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:30:25.294] request_log.py->create_log line:20 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 65527), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x00000224B52B2E40>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000224B58CA210>>, '_send': <function empty_send at 0x00000224B24F16C0>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x00000224B584ADC0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:30:26.933] request_log.py->create_log line:21 [INFO] : True
[2025-08-01 11:36:29.146] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:36:29.146] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:36:29.146] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:36:29.146] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:36:29.146] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:36:36.252] request_log.py->create_log line:21 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 49310), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x00000206CE2381A0>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000206CE23BE00>>, '_send': <function empty_send at 0x00000206CB24EC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x00000206CE25B760>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:36:36.253] request_log.py->create_log line:22 [INFO] : True
[2025-08-01 11:36:36.312] request_log.py->create_log line:49 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x00000206CF24A2D0>, 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'status': 1, 'path': '/v1/ai/chat/stream', 'query_params': '', 'body': None, 'response': None, 'created_time': datetime.datetime(2025, 8, 1, 11, 36, 34), 'is_deleted': 0, 'user_id': '', 'ip': '127.0.0.1', 'method': 'POST', 'path_params': '{}', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'response_status_code': None, 'id': 1, 'updated_time': datetime.datetime(2025, 8, 1, 11, 36, 34)}
[2025-08-01 11:39:43.655] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:39:43.656] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:39:43.656] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:39:43.656] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:39:43.656] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:39:51.406] request_log.py->create_log line:21 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 49477), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x00000257935EC050>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000257935EFE00>>, '_send': <function empty_send at 0x00000257905EEC00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x00000257935FB4C0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:39:51.406] request_log.py->create_log line:22 [INFO] : True
[2025-08-01 11:39:51.474] request_log.py->create_log line:49 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000002579361A2D0>, 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'status': 1, 'path': '/v1/ai/chat/stream', 'query_params': '', 'body': None, 'response': None, 'created_time': datetime.datetime(2025, 8, 1, 11, 39, 50), 'is_deleted': 0, 'method': 'POST', 'user_id': '', 'ip': '127.0.0.1', 'path_params': '{}', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'response_status_code': None, 'id': 2, 'updated_time': datetime.datetime(2025, 8, 1, 11, 39, 50)}
[2025-08-01 11:41:15.196] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:41:15.197] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:41:15.197] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:41:15.198] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:41:15.199] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:41:32.583] request_log.py->create_log line:21 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 49518), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x00000160CA1EAE40>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000160CA3DEA50>>, '_send': <function empty_send at 0x00000160C7421800>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x00000160CA3E7AE0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:41:33.951] request_log.py->create_log line:22 [INFO] : True
[2025-08-01 11:41:46.089] request_log.py->create_log line:49 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x00000160C8A06AB0>, 'user_id': '', 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'path': '/v1/ai/chat/stream', 'query_params': '', 'body': "{'session_id': 'string', 'message': 'string', 'model_type': 'tongyi', 'temperature': 0.7, 'max_tokens': 2000, 'stream': True}", 'response': None, 'created_time': datetime.datetime(2025, 8, 1, 11, 41, 43), 'is_deleted': 0, 'method': 'POST', 'status': 1, 'ip': '127.0.0.1', 'path_params': '{}', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'response_status_code': None, 'id': 3, 'updated_time': datetime.datetime(2025, 8, 1, 11, 41, 43)}
[2025-08-01 11:42:36.975] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:42:36.976] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:42:36.976] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:42:36.977] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:42:36.978] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:42:44.287] request_log.py->create_log line:21 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 49552), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001E9B6496E40>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001E9B668EA50>>, '_send': <function empty_send at 0x000001E9B36C1800>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001E9B6697AE0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:42:44.775] request_log.py->create_log line:22 [INFO] : True
[2025-08-01 11:42:50.012] request_log.py->create_log line:49 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000001E9B4D447D0>, 'user_id': '', 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'path': '/v1/ai/chat/stream', 'query_params': '', 'body': "{'session_id': 'string', 'message': 'string', 'model_type': 'tongyi', 'temperature': 0.7, 'max_tokens': 2000, 'stream': True}", 'response': None, 'created_time': datetime.datetime(2025, 8, 1, 11, 42, 47), 'is_deleted': 0, 'ip': '127.0.0.1', 'status': 1, 'method': 'POST', 'path_params': '{}', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'response_status_code': None, 'id': 4, 'updated_time': datetime.datetime(2025, 8, 1, 11, 42, 47)}
[2025-08-01 11:46:59.730] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:46:59.731] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:46:59.732] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:46:59.732] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:46:59.733] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:48:13.007] request_log.py->create_log line:21 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 49640), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x0000025094B8AE40>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000025094D7EA50>>, '_send': <function empty_send at 0x0000025091DA1800>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x0000025094D8BBC0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:48:13.349] request_log.py->create_log line:22 [INFO] : True
[2025-08-01 11:48:18.227] request_log.py->create_log line:49 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x0000025094E6A5D0>, 'ip': '127.0.0.1', 'method': 'POST', 'status': 1, 'path_params': '{}', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'response_status_code': None, 'id': 5, 'updated_time': datetime.datetime(2025, 8, 1, 11, 48, 15), 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'user_id': '', 'path': '/v1/ai/chat/stream', 'query_params': '', 'body': "{'session_id': 'string', 'message': 'string', 'model_type': 'tongyi', 'temperature': 0.7, 'max_tokens': 2000, 'stream': True}", 'response': None, 'created_time': datetime.datetime(2025, 8, 1, 11, 48, 15), 'is_deleted': 0}
[2025-08-01 11:49:36.091] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:49:36.092] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:49:36.093] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:49:36.094] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:49:36.094] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:49:48.223] request_log.py->create_log line:21 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 49677), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x0000025DB8DC6E40>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000025DB8FBEA50>>, '_send': <function empty_send at 0x0000025DB5FF1800>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x0000025DB8FCBA00>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:49:48.416] request_log.py->create_log line:22 [INFO] : True
[2025-08-01 11:49:52.219] request_log.py->create_log line:49 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x0000025DB90AA690>, 'user_id': '', 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'path': '/v1/ai/chat/stream', 'query_params': '', 'body': "{'session_id': 'string', 'message': 'string', 'model_type': 'tongyi', 'temperature': 0.7, 'max_tokens': 2000, 'stream': True}", 'response': None, 'created_time': datetime.datetime(2025, 8, 1, 11, 49, 50), 'is_deleted': 0, 'method': 'POST', 'status': 1, 'ip': '127.0.0.1', 'path_params': '{}', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'response_status_code': None, 'id': 6, 'updated_time': datetime.datetime(2025, 8, 1, 11, 49, 50)}
[2025-08-01 11:50:06.576] request_log.py->update_log line:73 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x0000025DB90AA690>}
[2025-08-01 11:56:59.554] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:56:59.554] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:56:59.554] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:56:59.554] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:56:59.554] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 11:57:04.976] request_log.py->create_log line:21 [INFO] : {'scope': {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8001), 'client': ('127.0.0.1', 51339), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/v1/ai/chat/stream', 'raw_path': b'/v1/ai/chat/stream', 'query_string': b'', 'headers': [(b'host', b'127.0.0.1:8001'), (b'connection', b'keep-alive'), (b'content-length', b'139'), (b'sec-ch-ua-platform', b'"Windows"'), (b'user-agent', b'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'), (b'accept', b'application/json'), (b'sec-ch-ua', b'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"'), (b'content-type', b'application/json'), (b'sec-ch-ua-mobile', b'?0'), (b'origin', b'http://127.0.0.1:8001'), (b'sec-fetch-site', b'same-origin'), (b'sec-fetch-mode', b'cors'), (b'sec-fetch-dest', b'empty'), (b'referer', b'http://127.0.0.1:8001/docs'), (b'accept-encoding', b'gzip, deflate, br, zstd'), (b'accept-language', b'zh-CN,zh;q=0.9')], 'state': {}, 'app': <fastapi.applications.FastAPI object at 0x000001B4A785C050>}, '_receive': <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x000001B4A785FE00>>, '_send': <function empty_send at 0x000001B4A4862C00>, '_stream_consumed': False, '_is_disconnected': False, '_form': None, '_wrapped_rcv_disconnected': False, '_wrapped_rcv_consumed': False, '_wrapped_rc_stream': <async_generator object Request.stream at 0x000001B4A786B4C0>, '_url': URL('http://127.0.0.1:8001/v1/ai/chat/stream'), '_headers': Headers({'host': '127.0.0.1:8001', 'connection': 'keep-alive', 'content-length': '139', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'origin': 'http://127.0.0.1:8001', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://127.0.0.1:8001/docs', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9'})}
[2025-08-01 11:57:04.977] request_log.py->create_log line:22 [INFO] : True
[2025-08-01 11:57:05.014] request_log.py->create_log line:49 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000001B4A8856210>, 'url': 'http://127.0.0.1:8001/v1/ai/chat/stream', 'status': 1, 'path': '/v1/ai/chat/stream', 'query_params': '', 'body': "{'session_id': 'string', 'message': 'string', 'model_type': 'tongyi', 'temperature': 0.7, 'max_tokens': 2000, 'stream': True}", 'response': None, 'created_time': datetime.datetime(2025, 8, 1, 11, 57, 3), 'is_deleted': 0, 'method': 'POST', 'user_id': '', 'ip': '127.0.0.1', 'path_params': '{}', 'header': '{\'host\': \'127.0.0.1:8001\', \'connection\': \'keep-alive\', \'content-length\': \'139\', \'sec-ch-ua-platform\': \'"Windows"\', \'user-agent\': \'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\', \'accept\': \'application/json\', \'sec-ch-ua\': \'"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"\', \'content-type\': \'application/json\', \'sec-ch-ua-mobile\': \'?0\', \'origin\': \'http://127.0.0.1:8001\', \'sec-fetch-site\': \'same-origin\', \'sec-fetch-mode\': \'cors\', \'sec-fetch-dest\': \'empty\', \'referer\': \'http://127.0.0.1:8001/docs\', \'accept-encoding\': \'gzip, deflate, br, zstd\', \'accept-language\': \'zh-CN,zh;q=0.9\'}', 'response_status_code': None, 'id': 7, 'updated_time': datetime.datetime(2025, 8, 1, 11, 57, 3)}
[2025-08-01 11:57:05.047] request_log.py->update_log line:73 [INFO] : {'_sa_instance_state': <sqlalchemy.orm.state.InstanceState object at 0x000001B4A8856210>}
[2025-08-01 11:58:21.274] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 11:58:21.275] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 11:58:21.275] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 11:58:21.275] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 11:58:21.275] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 14:47:14.011] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 14:47:14.012] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 14:47:14.012] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 14:47:14.012] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 14:47:14.012] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
[2025-08-01 15:19:15.658] __init__.py-><module> line:17 [INFO] : 数据库表message创建成功！
[2025-08-01 15:19:15.659] __init__.py-><module> line:17 [INFO] : 数据库表session创建成功！
[2025-08-01 15:19:15.659] __init__.py-><module> line:17 [INFO] : 数据库表user创建成功！
[2025-08-01 15:19:15.659] __init__.py-><module> line:17 [INFO] : 数据库表event_log创建成功！
[2025-08-01 15:19:15.659] __init__.py-><module> line:17 [INFO] : 数据库表request_log创建成功！
