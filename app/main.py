import json

import uvicorn
from fastapi import Fast<PERSON><PERSON>, <PERSON><PERSON>, Depends
from fastapi.exceptions import RequestValidationError, HTTPException
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi_offline import FastAPIOffline
from sqlalchemy import false

from app.api.controller.base import base_router
from app.config.config import Config
from app.config.fastapi import FastapiConfig
from app.utils.auth import get_auth_data_by_authorization
from app.utils.db import DbUtils
from app.utils.redis import RedisUtils
from app.utils.request_log import create_log, update_log
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request
from starlette.responses import Response
from starlette.staticfiles import StaticFiles
from config.anonymous import anonymous_path_list
from app.api.v1.api_v1 import api_v1_router
from app.utils.log import logger


# 定义依赖项：验证请求头中的 Token
def verify_token(x_token: str = Header(..., description="全局访问令牌（必填）")):
    if x_token != "valid_token":
        raise HTTPException(status_code=403, detail="无效的 Token")
    return x_token

logger.debug(Config.fastapi.__dict__)
# app = FastAPIOffline(**Config.fastapi.__dict__,dependencies=[Depends(verify_token)])
app = FastAPIOffline(**Config.fastapi.__dict__)




@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    await update_log(request.state.log, Response(json.dumps({
        'code': 400,
        'message': 'Request Validation Error',
        'error_detail': str(exc),
    }), status_code=400))

    return Response(json.dumps({
        'code': 400,
        'message': 'Request Validation Error',
    }), status_code=400)


@app.exception_handler(Exception)
async def http_exception_handler(request, exc):
    await update_log(request.state.log, Response(json.dumps({
        'code': 500,
        'message': 'Internal Server Error',
        'error_detail': str(exc),
    }), status_code=500))

    return Response(json.dumps({
        'code': 500,
        'message': 'Internal Server Error',
    }), status_code=500)


@app.middleware('http')
async def auth_token(req: Request, call_next):
    response_type = 1
    response = Response(json.dumps({
        'code': 401,
        'message': 'Unauthorized',
    }), status_code=401)
    path = req.method + req.url.path

    # 判断是否可匿名访问
    if path in anonymous_path_list or path.find('GET/static/') == 0:
        response_type = 2
        response = await call_next(req)
    else:
        auth_data = get_auth_data_by_authorization(req.headers.get('authorization'), 360000)

        if auth_data:
            response_type = 3
            response = await call_next(req)

    if response_type == 1:
        # logger.error(req.json())
        log = await create_log(req)
        await update_log(log, response)

    return response


# 自定义 OpenAPI 文档路由
@app.get("/openapi.json", include_in_schema=False)
async def get_openapi():
    return app.openapi()

app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)

app.include_router(base_router)
app.include_router(api_v1_router, prefix='/v1')
# app.mount('./res', StaticFiles(directory=FastapiConfig.res_path), name='res')
app.mount("/static", StaticFiles(directory="static"), name="static")



if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8001)