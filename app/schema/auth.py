from app.schema.base import UserBaseSchema, BaseSchema
from pydantic import BaseModel


class AuthDataSchema(BaseModel):
    code: int = 0
    message: str = 'SUCCESS'
    token: str = None
    user_id: int = None
    user: UserBaseSchema = None


class LoginInputSchema(BaseModel):
    type: str
    login_name: str
    token: str
    password: str = None

class UserSchema(BaseSchema):
    login_name: str
    username : str
    password : str
