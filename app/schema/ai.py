from datetime import datetime
from typing import List, Optional
from enum import Enum

from app.schema.base import BaseSchema, RespBaseSchema


class ModelType(str, Enum):
    """AI模型类型枚举"""
    TONGYI = "tongyi"  # 通义千问
    OPENAI = "openai"  # OpenAI GPT
    CLAUDE = "claude"  # Anthropic Claude
    GEMINI = "gemini"  # Google Gemini
    BAIDU = "baidu"  # 百度文心一言


class ChatMessageSchema(BaseSchema):
    """聊天消息Schema"""
    role: str  # 角色: system, user, assistant
    content: str  # 消息内容


class ChatRequestSchema(BaseSchema):
    """聊天请求Schema"""
    session_id: str  # 会话ID
    message: str  # 用户消息
    model_type: ModelType = ModelType.TONGYI  # 模型类型，默认通义千问
    temperature: Optional[float] = 0.7  # 温度参数
    max_tokens: Optional[int] = 2000  # 最大token数
    stream: bool = True  # 是否流式输出


class ChatResponseSchema(RespBaseSchema):
    """聊天响应Schema"""
    session_id: str  # 会话ID
    message_id: int  # 消息ID
    content: str  # AI回复内容
    model_type: str  # 使用的模型类型
    tokens_used: Optional[int] = None  # 使用的token数


class SessionCreateSchema(BaseSchema):
    """创建会话Schema"""
    title: Optional[str] = "新对话"  # 会话标题


class SessionResponseSchema(RespBaseSchema):
    """会话响应Schema"""
    session_id: str  # 会话ID
    title: str  # 会话标题
    created_time: datetime  # 创建时间


class MessageHistorySchema(BaseSchema):
    """消息历史Schema"""
    id: int  # 消息ID
    session_id: str  # 会话ID
    type: int  # 消息类型
    content: str  # 消息内容
    created_time: datetime  # 创建时间


class ChatHistoryResponseSchema(RespBaseSchema):
    """聊天历史响应Schema"""
    session_id: str  # 会话ID
    messages: List[MessageHistorySchema]  # 消息列表
