from sqlalchemy import Column,  String,text
from sqlalchemy.dialects.mysql import BIGINT,TIMESTAMP,TINYINT,LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

DeclarativeBase = declarative_base()


class BaseModel(DeclarativeBase):
    """
    基础Model模型对象
    """
    __abstract__ = True

    id = Column(BIGINT(20), primary_key=True,autoincrement=True, comment='序号')
    created_time = Column(TIMESTAMP, nullable=False, server_default=text("current_timestamp()"), comment='创建时间')
    updated_time = Column(TIMESTAMP, nullable=True, server_default=text("current_timestamp() ON UPDATE current_timestamp()"), comment='更新时间')
    is_deleted = Column(TINYINT(1), nullable=True, server_default=text("0"), comment='软删')

    class Config:
        orm_mode = True  # 支持从 ORM 模型实例转换

