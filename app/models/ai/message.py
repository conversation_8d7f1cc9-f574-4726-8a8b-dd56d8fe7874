import enum

from sqlalchemy import Enum, TEXT, String

from app.models.basemodel import *


class MsgType(enum.IntEnum):
    OTHER = -1
    NONE = 0
    SYS = 1
    USER = 2
    ASSISTANT = 3  # AI助手回复

class ModelType(enum.IntEnum):
    Tongyi = 1
    Openai = 2
    Claude = 3
    Gemini = 4
    Baidu = 5


class Message(BaseModel):
    __tablename__ = 'message'
    __table_args__ = {'comment': '消息'}

    session_id = Column(String(255), nullable=False, index=True, comment='会话ID')
    msg_type = Column(Enum(MsgType), nullable=False, comment='消息类型')
    content = Column(TEXT, nullable=False, comment='消息内容')
    model_type = Column(Enum(ModelType), nullable=True, comment='AI模型类型')
    tokens_used = Column(String(20), nullable=True, comment='使用的token数')
    user_id = Column(String(255), nullable=False, comment='用户ID')


class Session(BaseModel):
    __tablename__ = 'session'
    __table_args__ = {'comment': '会话'}

    session_id = Column(String(255), nullable=False, unique=True, index=True, comment='会话ID')
    title =  Column(String(255), nullable=False, comment='标题')
    user_id = Column(String(255), nullable=False, comment='用户ID')
