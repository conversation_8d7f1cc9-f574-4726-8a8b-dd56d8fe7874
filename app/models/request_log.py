from sqlalchemy import Column

from app.models.basemodel import *


class RequestLog(BaseModel):
    __tablename__ = 'request_log'
    __table_args__ = {'comment': '事件记录'}

    status = Column(TINYINT, nullable=True, server_default=text("'1'"), comment='状态：1：请求日志，2：已响应')
    user_id = Column(String(255), nullable=True, server_default=text("''"), comment='用戶ID')
    ip = Column(String(255), nullable=True, server_default=text("''"), comment='IP地址')
    url = Column(String(255), nullable=True, server_default=text("''"), comment='URL')
    method = Column(String(255), nullable=True, server_default=text("''"), comment='方法')
    path = Column(String(255), nullable=True, server_default=text("''"), comment='路径')
    path_params = Column(LONGTEXT, nullable=True, comment='路径参数')
    query_params = Column(LONGTEXT, nullable=True,  comment='请求参数')
    header = Column(LONGTEXT, nullable=True, comment='请求头')  # request.headers.items()
    body = Column(LONGTEXT, nullable=True,  comment='请求体')
    response_status_code = Column(LONGTEXT, nullable=True, comment='响应状态码')
    response = Column(LONGTEXT, nullable=True, comment='响应信息')