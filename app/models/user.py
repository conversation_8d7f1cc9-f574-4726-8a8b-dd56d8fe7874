from sqlalchemy import String

from app.models.basemodel import *


class User(BaseModel):
    __tablename__ = 'user'
    __table_args__ = {'comment': '用户'}

    login_name = Column(String(255), unique=True, nullable=False, server_default=text("''"), comment='账号')
    username = Column(String(255), nullable=False, server_default=text("''"), comment='用户名')
    password = Column(String(255), nullable=False, server_default=text("''"), comment='密码')


