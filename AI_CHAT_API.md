# AI聊天服务API文档

## 概述

本项目实现了支持多个大模型的AI聊天服务，具有以下特性：

- 🤖 **多模型支持**：通义千问、OpenAI GPT、<PERSON>、Gemini、百度文心一言
- 💬 **流式响应**：实时流式输出AI回复
- 📝 **消息持久化**：自动保存聊天记录到数据库
- 🔄 **会话管理**：支持多会话管理和历史记录查询
- 🔐 **用户认证**：基于Token的用户认证

## API接口

### 1. 创建聊天会话

**POST** `/v1/ai/session/create`

```json
{
  "title": "新对话"
}
```

**响应：**
```json
{
  "code": 0,
  "message": "SUCCESS",
  "session_id": "uuid-string",
  "title": "新对话",
  "created_time": "2024-01-01T12:00:00"
}
```

### 2. 流式聊天

**POST** `/v1/ai/chat/stream`

```json
{
  "session_id": "uuid-string",
  "message": "你好，请介绍一下Python",
  "model_type": "tongyi",
  "temperature": 0.7,
  "max_tokens": 2000,
  "stream": true
}
```

**响应：** Server-Sent Events (SSE) 流式数据

```
data: {"content": "你好", "type": "chunk"}

data: {"content": "！Python", "type": "chunk"}

data: {"content": "是一种...", "type": "chunk"}

data: {"content": "", "type": "done", "message_id": 123}
```

### 3. 获取聊天历史

**GET** `/v1/ai/session/{session_id}/history`

**响应：**
```json
{
  "code": 0,
  "message": "SUCCESS",
  "session_id": "uuid-string",
  "messages": [
    {
      "id": 1,
      "session_id": "uuid-string",
      "type": 2,
      "content": "你好，请介绍一下Python",
      "created_time": "2024-01-01T12:00:00"
    },
    {
      "id": 2,
      "session_id": "uuid-string",
      "type": 3,
      "content": "你好！Python是一种...",
      "created_time": "2024-01-01T12:00:01"
    }
  ]
}
```

## 支持的模型类型

- `tongyi` - 通义千问（阿里云）
- `openai` - OpenAI GPT（待实现）
- `claude` - Anthropic Claude（待实现）
- `gemini` - Google Gemini（待实现）
- `baidu` - 百度文心一言（待实现）

## 消息类型

- `1` - 系统消息 (SYS)
- `2` - 用户消息 (USER)
- `3` - AI助手消息 (ASSISTANT)

## 环境配置

### 通义千问配置

设置环境变量：
```bash
export DASHSCOPE_API_KEY="your-api-key"
```

### 数据库配置

确保MySQL数据库已配置并运行，相关配置在 `app/config/db.py` 中。

## 使用示例

### Python客户端示例

```python
import requests
import json

# 1. 创建会话
response = requests.post('http://localhost:8001/v1/ai/session/create', 
                        json={'title': '测试对话'},
                        headers={'Authorization': 'your-token'})
session_data = response.json()
session_id = session_data['session_id']

# 2. 发送消息（流式）
response = requests.post('http://localhost:8001/v1/ai/chat/stream',
                        json={
                            'session_id': session_id,
                            'message': '你好，请介绍一下Python',
                            'model_type': 'tongyi'
                        },
                        headers={'Authorization': 'your-token'},
                        stream=True)

# 处理流式响应
for line in response.iter_lines():
    if line:
        data = line.decode('utf-8')
        if data.startswith('data: '):
            content = json.loads(data[6:])
            print(content['content'], end='')

# 3. 获取历史记录
response = requests.get(f'http://localhost:8001/v1/ai/session/{session_id}/history',
                       headers={'Authorization': 'your-token'})
history = response.json()
```

### JavaScript客户端示例

```javascript
// 使用EventSource处理SSE
const eventSource = new EventSource('/v1/ai/chat/stream', {
  method: 'POST',
  headers: {
    'Authorization': 'your-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    session_id: 'your-session-id',
    message: '你好，请介绍一下Python',
    model_type: 'tongyi'
  })
});

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  if (data.type === 'chunk') {
    document.getElementById('chat-content').innerHTML += data.content;
  } else if (data.type === 'done') {
    console.log('对话完成，消息ID:', data.message_id);
    eventSource.close();
  }
};
```

## 数据库表结构

### message表
- `id` - 消息ID
- `session_id` - 会话ID
- `type` - 消息类型
- `content` - 消息内容
- `model_type` - AI模型类型
- `tokens_used` - 使用的token数
- `user_id` - 用户ID
- `created_time` - 创建时间
- `updated_time` - 更新时间

### session表
- `id` - 会话ID
- `session_id` - 会话唯一标识
- `title` - 会话标题
- `user_id` - 用户ID
- `created_time` - 创建时间
- `updated_time` - 更新时间

## 注意事项

1. 所有接口都需要在请求头中包含有效的 `Authorization` token
2. 流式响应使用 Server-Sent Events (SSE) 格式
3. 消息内容会自动保存到数据库中
4. 每个会话的历史消息会在新对话时自动加载作为上下文
5. 目前只实现了通义千问模型，其他模型需要额外配置相应的API密钥
